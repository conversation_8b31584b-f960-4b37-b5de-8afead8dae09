# Cluster Assignment Functionality

This document explains how to use the new cluster assignment functionality that automatically assigns cluster IDs to images that don't have them.

## Overview

The cluster assignment function works by:

1. **Finding unassigned images**: Locates images in the database that don't have a `cluster_id` assigned
2. **Vector lookup**: Uses `vectorRepo.find_by_url()` to get the feature vector for the image
3. **Similarity search**: Uses the vector database to find similar images
4. **Cluster assignment**:
   - If similar images with existing cluster IDs are found (above the similarity threshold), assigns the same cluster ID
   - If no similar images are found, creates a new cluster ID (max + 1)
5. **Iteration**: Repeats until all processable images have cluster IDs

**Important**: This function only processes images that already exist in the vector database. Images not found in the vector database will be skipped.

## Usage

### Method 1: Using the Example Script

```bash
# Run cluster assignment with default similarity threshold (0.7)
python example_cluster_assignment.py

# Check status of cluster assignment
python example_cluster_assignment.py status

# Set custom similarity threshold
SIMILARITY_THRESHOLD=0.8 python example_cluster_assignment.py
```

### Method 2: Programmatic Usage

```python
from app.dependencies.dependencies import get_clustering_service

# Get the clustering service
clustering_service = get_clustering_service()

# Run cluster assignment with custom threshold
clustering_service.assign_cluster_ids(similarity_threshold=0.75)
```

### Method 3: Direct Integration

```python
from app.containers import Container
from app.services.clustering import Clustering

# Initialize container
container = Container()
container.config.use_remote.from_env("USE_REMOTE_REPO", default="true")

# Get services
repo = container.similarity_repo()
vector_repo = container.vector_db()

# Create clustering service
clustering = Clustering(repo, vector_repo)

# Run assignment
clustering.assign_cluster_ids(similarity_threshold=0.7)
```

## Configuration

### Environment Variables

- `USE_REMOTE_REPO`: Set to "true" to use MongoDB, "false" for local files
- `SIMILARITY_THRESHOLD`: Similarity threshold for clustering (default: 0.7)

### Similarity Threshold

The similarity threshold determines how similar two images need to be to be considered part of the same cluster:

- **0.9-1.0**: Very strict - only nearly identical images
- **0.7-0.9**: Moderate - similar images with some variation
- **0.5-0.7**: Loose - images with general similarity
- **Below 0.5**: Very loose - may group dissimilar images

## Process Flow

```
Start
  ↓
Find image without cluster_id
  ↓
Get features from vector DB using find_by_url
  ↓
Features found?
  ↓        ↓
 Yes      No
  ↓        ↓
Search    Skip
similar   image
images    ↓
  ↓        ↓
Calculate  ↓
similarity ↓
scores     ↓
  ↓        ↓
Found similar image with cluster_id above threshold?
  ↓                    ↓
 Yes                  No
  ↓                    ↓
Assign same         Create new
cluster_id          cluster_id
  ↓                    ↓
Update database ←------┘
  ↓
More unassigned images?
  ↓        ↓
 Yes      No
  ↓        ↓
Loop     End
```

## Repository Methods Added

The following methods were added to support cluster assignment:

### SimilarityRepository

```python
def find_one_without_cluster_id(self) -> Optional[Dict[str, Any]]:
    """Find one document that doesn't have a cluster_id assigned."""

def get_max_cluster_id(self) -> int:
    """Get the maximum cluster_id currently in the database."""

def update_cluster_id(self, url: str, cluster_id: int) -> bool:
    """Update the cluster_id for a specific image URL."""
```

### SimilarityVector

```python
def find_by_url(self, url: str):
    """Find vector data by URL. Returns dict with 'url' and 'feature' keys if found, None if not found."""
```

## Error Handling

The function includes robust error handling:

- **Vector not found**: Skips images not found in vector database
- **Database errors**: Logs errors and continues with next image
- **Network issues**: Handles timeouts and connection problems
- **Invalid data**: Skips problematic images and continues

## Performance Considerations

- **Batch processing**: Processes images one at a time to avoid memory issues
- **Progress logging**: Logs progress every 10 images
- **Efficient queries**: Uses optimized database queries
- **Vector search**: Limits similarity search to top 20 results

## Monitoring

The function provides detailed logging:

```
INFO - Starting cluster assignment with similarity threshold: 0.7
INFO - Processing image without cluster_id: https://example.com/image1.jpg
INFO - Found similar image https://example.com/image2.jpg with cluster_id 5, similarity: 0.85
INFO - Assigned existing cluster_id 5 to https://example.com/image1.jpg
INFO - Processing image without cluster_id: https://example.com/image2.jpg
INFO - No similar images found for https://example.com/image2.jpg, assigned new cluster_id 6
INFO - Vector data not found for https://example.com/image3.jpg, skipping...
INFO - Progress: 10 images processed, 2 skipped
INFO - All data has been assigned cluster IDs. Total processed: 25, skipped: 5
```

## Troubleshooting

### Common Issues

1. **Vector data not found**: Ensure images exist in the vector database first
2. **No similar images found**: Lower the similarity threshold or check vector database
3. **Database connection issues**: Verify MongoDB/vector database connectivity
4. **Images being skipped**: Check if images have been processed and added to vector database

### Debug Mode

Enable debug logging to see detailed similarity scores:

```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

## Integration with Existing Workflow

This function complements the existing clustering workflow:

1. **Initial clustering**: Use `build_cluster()` for bulk clustering of all images
2. **Incremental assignment**: Use `assign_cluster_ids()` for new images without cluster IDs
3. **Re-clustering**: Use `build_cluster()` to rebuild all clusters when needed
