#!/usr/bin/env python3
"""
Example script demonstrating how to use the cluster assignment functionality.

This script shows how to:
1. Set up the necessary services and repositories
2. Call the assign_cluster_ids function to assign cluster IDs to images without them
3. Monitor the progress

Note: This function only assigns cluster IDs to images that already exist in the vector database.
Images not found in the vector database will be skipped.

Usage:
    python example_cluster_assignment.py

Environment Variables:
    USE_REMOTE_REPO: Set to "true" to use MongoDB, "false" for local files
    SIMILARITY_THRESHOLD: Similarity threshold for clustering (default: 0.7)
"""

import os
import sys
import logging
from app.containers import Container
from app.dependencies.dependencies import get_clustering_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    """Main function to run cluster assignment."""
    
    # Get similarity threshold from environment or use default
    similarity_threshold = float(os.environ.get('SIMILARITY_THRESHOLD', '0.7'))
    
    print(f"Starting cluster assignment with similarity threshold: {similarity_threshold}")
    print("This process will:")
    print("1. Find images without cluster_id")
    print("2. Get features from vector database using find_by_url")
    print("3. Find similar images and assign cluster_id based on similar images")
    print("4. Create new cluster_id if no similar images found")
    print("5. Skip images not found in vector database")
    print("6. Continue until all processable images have cluster_id")
    print()
    
    try:
        # Get the clustering service (this will automatically inject all dependencies)
        clustering_service = get_clustering_service()

        print("Services initialized successfully.")
        print("Starting cluster assignment process...")
        print()
        
        # Run the cluster assignment
        clustering_service.assign_cluster_ids(similarity_threshold=similarity_threshold)
        
        print()
        print("Cluster assignment completed successfully!")
        
    except KeyboardInterrupt:
        print("\nProcess interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"ERROR: An error occurred during cluster assignment: {e}")
        logging.exception("Detailed error information:")
        sys.exit(1)

def check_status():
    """Check how many images don't have cluster_id assigned."""
    try:
        clustering_service = get_clustering_service()
        repo = clustering_service.repo
        
        # Count unassigned images
        count = 0
        while True:
            unassigned = repo.find_one_without_cluster_id()
            if not unassigned:
                break
            count += 1
            if count >= 100:  # Limit check to avoid infinite loop
                print(f"Found at least {count} images without cluster_id (stopped counting at 100)")
                return
        
        if count == 0:
            print("All images have cluster_id assigned!")
        else:
            print(f"Found {count} images without cluster_id")
            
        # Get max cluster_id
        max_cluster_id = repo.get_max_cluster_id()
        print(f"Current maximum cluster_id: {max_cluster_id}")
        
    except Exception as e:
        print(f"ERROR: Failed to check status: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # Check command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "status":
            print("Checking cluster assignment status...")
            check_status()
        elif sys.argv[1] == "help":
            print(__doc__)
        else:
            print("Unknown command. Use 'status' or 'help'")
            sys.exit(1)
    else:
        main()
